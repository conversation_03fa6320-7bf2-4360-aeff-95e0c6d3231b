from typing import List, Dict

from modules.common.schema import CodeSnippet
from modules.common.schema import SearchToolEnum
from modules.common.search_tool import SearchToolABC


class SearchManager:
    """搜索管理器"""
    
    def __init__(self, repo_path: str, search_type: SearchToolEnum = "grep"):
        """
        初始化搜索管理器
        
        Args:
            repo_path: 仓库路径
            search_type: 搜索类型 ("grep" 或 "embedding")
        """
        self.repo_path = repo_path
        self.search_type = search_type
        self.search_instance: SearchToolABC = search_type.search_class(repo_path=repo_path)
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        执行搜索
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        return self.search_instance.search(query)
    
    def search_multiple(self, queries: List[str]) -> Dict[str, List[CodeSnippet]]:
        """
        并行执行多个搜索查询
        
        Args:
            queries: 查询字符串列表
            
        Returns:
            Dict[str, List[CodeSnippet]]: 以查询为键的搜索结果字典
        """
        results = {}
        for query in queries:
            results[query] = self.search(query)
        return results
