"""
DeepSearch核心模块
实现完整的深度搜索流程
"""
from typing import List, Dict

from core.config import get_config
from modules.llm.llm_client import LLMClient, default_llm_client
from backend.python.modules.common.schema import SearchResult, CodeSnippet, SearchToolEnum
from modules.deepsearch.prompts import (
    QUERY_SPLIT_PROMPT, 
    SUBQUERY_FILTER_PROMPT, 
    GENERATE_NEW_QUERY_PROMPT,
    SYSTEM_PROMPTS
)
from modules.deepsearch.search import SearchManager
from utils.logger import logger

class DeepSearch:
    """深度搜索主类"""
    
    def __init__(
        self, 
        repo_path: str, 
        repo_info: str = "",
        llm_client: LLMClient = None,
        search_tools: List[SearchToolEnum] = [SearchToolEnum.GREP]
    ):
        """
        初始化DeepSearch
        
        Args:
            repo_path: 仓库路径
            repo_info: 仓库信息描述
            llm_client: LLM客户端，如果为None则使用默认客户端
            search_type: 搜索类型
        """
        self.repo_path = repo_path
        self.repo_info = repo_info
        self.search_tools = search_tools

        self.client = llm_client or default_llm_client
        self.search_manager = SearchManager(repo_path, search_tools)
        self.config = get_config().deepsearch
    
    def search(self, query: str) -> SearchResult:
        """
        执行深度搜索
        
        Args:
            query: 用户查询
            
        Returns:
            SearchResult: 搜索结果
        """
        result = SearchResult(original_query=query)
        
        logger.info(f"Start DeepSearch for Query: {query}")
        for iteration in range(self.config.max_iterations):
            result.iterations = iteration + 1

            # 判断是否需要生成新查询
            new_queries = self._generate_new_queries(
                query, 
                result.all_queries, 
                result.code_snippets
            )
            
            if not new_queries:
                logger.info(f"No New Queries Generated, Search Completed")
                break
            
            logger.info(f"Iteration {iteration + 1}: {query}")
            logger.info(f"Iteration {iteration + 1}: Generated {len(new_queries)} New Queries: {new_queries}")

            result.all_queries.extend(new_queries)
            
            # 搜索新查询
            new_snippets = self._search_and_filter(new_queries, query)
            result.code_snippets.extend(new_snippets)
            
            if not new_snippets:
                logger.info("新查询未找到相关代码，搜索结束")
                break
        
        # 4. 文件级别合并和去重
        logger.info("Merging Code Snippets at File Level...")
        result.file_level_results = self._merge_by_file(result.code_snippets)
        
        logger.info(f"DeepSearch Completed: {result.get_summary()}")
        
        return result
    
    def _split_query(self, query: str, search_type: str) -> List[str]:
        """
        拆分查询为子查询
        
        Args:
            query: 原始查询
            search_type: 搜索类型
        Returns:
            List[str]: 子查询列表
        """
        prompt = QUERY_SPLIT_PROMPT.format(
            original_query=query,
            search_type = search_type
        )
        
        sub_queries = self.llm_client.call_json(
            prompt, 
            SYSTEM_PROMPTS["query_split"]
        )
        
        # 限制子查询数量
        return sub_queries[:self.config.max_sub_queries]

    
    def search_worker(self, query):
        """搜索工作函数（支持多进程）"""
        try:
            snippets = self.search_manager.search(query)
            print(f"  查询 '{query}' 找到 {len(snippets)} 个代码片段")
            return query, snippets
        except Exception as exc:
            print(f"  查询 '{query}' 检索时发生异常: {exc}")
            return query, []
    
    def _search_and_filter(self, queries: List[str], original_query: str) -> List[CodeSnippet]:
        """
        搜索并过滤代码片段
        
        Args:
            queries: 查询列表
            original_query: 原始查询（用于过滤）
            
        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        # 并行检索所有子查询
        
        search_results = {}
        
        # 使用线程池进行并发搜索（避免多进程的序列化问题）
        import concurrent.futures
        max_workers = min(len(queries), 4)  # 限制线程数
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有搜索任务
            future_to_query = {
                executor.submit(self.search_manager.search, query): query
                for query in queries
            }
            
            # 收集搜索结果
            for future in concurrent.futures.as_completed(future_to_query):
                query = future_to_query[future]
                try:
                    snippets = future.result()
                    logger.info(f"Query '{query}' Found {len(snippets)} Code Snippets")
                    search_results[query] = snippets
                except Exception as exc:
                    logger.info(f"Query '{query}' Search Failed: {exc}")
                    search_results[query] = []

        # 统计总的代码片段数量
        total_snippets = sum(len(snippets) for snippets in search_results.values())
        logger.info(f"Found {total_snippets} Code Snippets, Start Filtering...")

        # 并行过滤每个子查询的代码片段
        all_filtered_snippets = []
        
        # 简化的并行过滤：对每个子查询的结果进行过滤
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_query = {}
            
            for sub_query, snippets in search_results.items():
                if snippets:  # 只处理有结果的查询
                    future = executor.submit(
                        self._filter_snippets_for_query, 
                        snippets, 
                        sub_query, 
                        original_query
                    )
                    future_to_query[future] = sub_query
            
            # 收集过滤结果
            for future in concurrent.futures.as_completed(future_to_query):
                sub_query = future_to_query[future]
                try:
                    filtered = future.result()
                    all_filtered_snippets.extend(filtered)
                    logger.info(f" Sub Query '{sub_query}' Filtered {len(filtered)} Snippets")
                except Exception as exc:
                    logger.info(f"Sub Query '{sub_query}' Filter Failed: {exc}")

        # 去重
        unique_snippets = self._deduplicate_snippets(all_filtered_snippets)
        logger.info(f"Filtered Snippets Deduplicated to {len(unique_snippets)} Unique Snippets")
        
        return unique_snippets

    def _filter_snippets_for_query(self, snippets: List[CodeSnippet], sub_query: str, original_query: str) -> List[CodeSnippet]:
        """
        为特定子查询过滤代码片段
        
        Args:
            snippets: 代码片段列表
            sub_query: 子查询
            original_query: 原始查询
            
        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        filtered_snippets = []
        
        # 使用子查询和原始查询的组合来判断相关性
        combined_query = f"Original Query: {original_query}\Sub Query: {sub_query}"
        
        for snippet in snippets:
            if self._is_relevant_snippet(snippet, combined_query):
                filtered_snippets.append(snippet)
        
        return filtered_snippets
    
    def _deduplicate_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        去重代码片段（基于文件路径和行号）
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            List[CodeSnippet]: 去重后的代码片段列表
        """
        seen = set()
        unique_snippets = []
        
        for snippet in snippets:
            # 使用文件路径和行号作为唯一标识
            key = (snippet.file_path, snippet.line_number)
            if key not in seen:
                seen.add(key)
                unique_snippets.append(snippet)
        
        return unique_snippets
    
    def _is_relevant_snippet(self, snippet: CodeSnippet, query: str) -> bool:
        """
        判断代码片段是否与查询相关
        
        Args:
            snippet: 代码片段
            query: 查询字符串
            
        Returns:
            bool: 是否相关
        """
        prompt = SUBQUERY_FILTER_PROMPT.format(
            repository_info=self.repo_info,
            query=query,
            code_snippet=snippet.get_full_content()
        )
        
        response = self.llm_client.call(prompt, SYSTEM_PROMPTS["filter"])
        return response.strip().upper() == "YES"
    
    def _generate_new_queries(
        self, 
        original_query: str, 
        previous_queries: List[str], 
        code_snippets: List[CodeSnippet]
    ) -> List[str]:
        """
        生成新的查询
        
        Args:
            original_query: 原始查询
            previous_queries: 之前的查询列表
            code_snippets: 已找到的代码片段
            
        Returns:
            List[str]: 新查询列表
        """
        # 构建代码片段摘要
        code_summary = self._build_code_summary(code_snippets)
        
        prompt = GENERATE_NEW_QUERY_PROMPT.format(
            question=original_query,
            mini_questions=previous_queries,
            code_snippet=code_summary
        )
        
        new_queries = self.llm_client.call_json(
            prompt,
            SYSTEM_PROMPTS["generate_new_query"]
        )
        
        # 限制新查询数量
        return new_queries[:self.config.max_new_queries]
    
    def _build_code_summary(self, snippets: List[CodeSnippet]) -> str:
        """
        构建代码片段摘要
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            str: 代码摘要
        """
        if not snippets:
            return "暂无相关代码片段"
        
        # 按文件分组
        file_groups = {}
        for snippet in snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)
        
        # 构建摘要
        summary_parts = []
        for file_path, file_snippets in file_groups.items():
            summary_parts.append(f"\n文件: {file_path}")
            for snippet in file_snippets[:3]:  # 每个文件最多显示3个片段
                summary_parts.append(f"  行 {snippet.line_number}: {snippet.content[:100]}...")
        
        return "\n".join(summary_parts)
    
    def _merge_by_file(self, snippets: List[CodeSnippet]) -> Dict[str, str]:
        """
        按文件合并代码片段并去重
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            Dict[str, str]: 文件路径到合并内容的映射
        """
        file_results = {}
        
        # 按文件分组
        file_groups = {}
        for snippet in snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)
        
        # 合并每个文件的代码片段
        for file_path, file_snippets in file_groups.items():
            # 按行号排序
            file_snippets.sort(key=lambda x: x.line_number)
            
            # 去重：合并重叠或相邻的片段
            merged_snippets = self._merge_overlapping_snippets(file_snippets)
            
            # 构建文件级结果
            file_content_parts = []
            for snippet in merged_snippets:
                file_content_parts.append(f"# 行 {snippet.line_number}:")
                file_content_parts.append(snippet.get_full_content())
                file_content_parts.append("")  # 空行分隔
            
            file_results[file_path] = "\n".join(file_content_parts)
        
        return file_results
    
    def _merge_overlapping_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        合并重叠或相邻的代码片段
        
        Args:
            snippets: 已排序的代码片段列表
            
        Returns:
            List[CodeSnippet]: 合并后的代码片段列表
        """
        if not snippets:
            return []
        
        merged = [snippets[0]]
        
        for current in snippets[1:]:
            last = merged[-1]
            
            # 计算片段的行号范围
            last_start = last.line_number - last.context_before.count('\n')
            last_end = last.line_number + last.context_after.count('\n')
            current_start = current.line_number - current.context_before.count('\n')
            current_end = current.line_number + current.context_after.count('\n')
            
            # 如果重叠或相邻（允许5行的间隔），则合并
            if current_start <= last_end + 5:
                # 合并代码片段
                merged_snippet = CodeSnippet(
                    file_path=last.file_path,
                    line_number=min(last.line_number, current.line_number),
                    content=f"{last.content}\n...\n{current.content}",
                    context_before=last.context_before,
                    context_after=current.context_after
                )
                merged[-1] = merged_snippet
            else:
                merged.append(current)
        
        return merged
