from enum import Enum
from typing import List, Dict, Any, Set
from pydantic import BaseModel, Field

from modules.common.search_tool import SearchToolABC

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"

    @property
    def description(self):
        if self == SearchToolEnum.GREP:
            return "grep: use command to match the keywords in code snippet, the query input should be specific keywords and searchable"
        elif self == SearchToolEnum.EMBEDDING:
            return "embedding: use vector database to search code, the query input should be general and cover all possible answers"
        else:
            return "未知搜索工具"

    @property
    def search_class(self) -> SearchToolABC:
        if self == SearchToolEnum.GREP:
            from modules.integration.tools.search import GrepSearchTool
            return GrepSearchTool
        elif self == SearchToolEnum.EMBEDDING:
            raise NotImplementedError("Embedding搜索暂未实现")
            # from modules.integration.embedding import EmbeddingSearchTool
            # return EmbeddingSearchTool
        else:
            raise ValueError("未知搜索工具")

# TODO: 重写以使其符合pydantic的写法
class CodeSnippet(BaseModel):
    """代码片段数据结构"""
    file_path: str
    line_number: int
    content: str
    context_before: str = ""
    context_after: str = ""
    
    def get_full_content(self) -> str:
        """获取包含上下文的完整内容"""
        full_content = []
        if self.context_before:
            full_content.append(self.context_before)
        full_content.append(self.content)
        if self.context_after:
            full_content.append(self.context_after)
        return "\n".join(full_content)

# TODO: 重写以使其符合pydantic的写法
class SearchResult(BaseModel):
    """搜索结果数据结构"""
    original_query: str
    sub_queries: List[str] = Field(default_factory=list)
    all_queries: List[str] = Field(default_factory=list)  # 包含生成的新查询
    code_snippets: List[CodeSnippet] = Field(default_factory=list)
    file_level_results: Dict[str, str] = Field(default_factory=dict)  # 文件级别的合并结果
    iterations: int = 0
    
    def get_summary(self) -> str:
        """获取搜索结果摘要"""
        return f"""搜索摘要:
- 原始查询: {self.original_query}
- 子查询数量: {len(self.sub_queries)}
- 总查询数量: {len(self.all_queries)}
- 找到的代码片段: {len(self.code_snippets)}
- 涉及文件数: {len(self.file_level_results)}
- 迭代次数: {self.iterations}
"""