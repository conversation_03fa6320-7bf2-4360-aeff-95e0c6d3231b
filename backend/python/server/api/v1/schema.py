from pydantic import BaseModel

# 数据模型
class WorkspaceInfo(BaseModel):
    id: str
    name: str
    path: str
    description: str = ""


# 搜索请求
class SearchRequest(BaseModel):
    query: str
    workspaceName: str
    searchType: str = "grep"  # 'grep' or 'embedding'
    maxResults: int = 50


# 搜索响应
class SearchResponse(BaseModel):
    searchId: str
    query: str
    result: str
    searchTime: float