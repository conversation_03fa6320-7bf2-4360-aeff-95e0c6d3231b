import time
import async<PERSON>
import j<PERSON>
from typing import List
from pathlib import Path
from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.responses import StreamingResponse

from server.api.v1.schema import WorkspaceInfo, SearchRequest, SearchResponse
from utils.file import FileNode

from core.config import get_config
from utils.logger import logger
from utils.file import should_ignore_path, build_file_tree, get_file_size

app = FastAPI()

@app.get("/api/workspaces", response_model=List[WorkspaceInfo])
async def get_workspaces():
    """获取所有工作区"""
    workspaces = []
    
    repos_path = Path(get_config().data.repos_path)
    if not repos_path.exists():
        raise HTTPException(status_code=404, detail="Repos directory not found")
    
    try:
        for item in repos_path.iterdir():
            if item.is_dir() and not should_ignore_path(item):
                description = f"{item.name} repository"
                
                workspace = WorkspaceInfo(
                    id=item.name,
                    name=item.name,
                    path=str(repos_path / item.name),
                    description=description
                )
                workspaces.append(workspace)
        
        return workspaces
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read workspaces: {str(e)}")


@app.get("/api/workspaces/{workspace_name}/files", response_model=List[FileNode])
async def get_workspace_files(workspace_name: str):
    """获取工作区文件树"""
    workspace_path = Path(get_config().data.repos_path) / workspace_name

    if not workspace_path.exists():
        raise HTTPException(status_code=404, detail="Workspace not found")

    if not workspace_path.is_dir():
        raise HTTPException(status_code=400, detail="Workspace is not a directory")
    
    try:
        file_tree = build_file_tree(workspace_path, workspace_name)
        return file_tree
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read file tree: {str(e)}")


@app.get("/api/files")
async def get_file_content(file_path: str = Query(..., description="File path relative to repos")):
    """获取文件内容"""
    # 安全检查：确保路径在 repos 目录内
    try:
        repos_path = Path(get_config().data.repos_path)
        full_path = repos_path / file_path
        logger.info(f"Full path: {full_path}")

        # 确保路径在 repos 目录内
        full_path = full_path.resolve()
        repos_path_resolved = repos_path.resolve()

        if not str(full_path).startswith(str(repos_path_resolved)):
            raise HTTPException(status_code=403, detail="Access denied")

        if not full_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        if not full_path.is_file():
            raise HTTPException(status_code=400, detail="Path is not a file")
        
        # 读取文件内容
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {"content": content}
        except UnicodeDecodeError:
            # 如果是二进制文件，返回提示信息
            return {"content": f"Binary file: {full_path.name}\nFile size: {get_file_size(full_path)} bytes"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read file: {str(e)}")


@app.post("/api/search")
async def search_code(request: SearchRequest):
    """处理代码搜索请求 - 流式返回搜索过程"""
    
    workspace_path = Path(get_config().data.repos_path) / request.workspaceName
    if not workspace_path.exists():
        raise HTTPException(status_code=404, detail="Workspace not found")

    # async def generate_search_stream():
    #     try:            
    #         # 构建命令
    #         project_root = Path(__file__).parent.parent.parent
    #         main_py_path = project_root / "main.py"
    #         python_path = project_root / ".venv" / "bin" / "python"

    #         if not python_path.exists():
    #             python_path = "python"

    #         cmd = [
    #             str(python_path),
    #             "-u",  # 无缓冲输出
    #             str(main_py_path),
    #             request.query,
    #             "--repo-path", str(workspace_path),
    #             "--search-type", request.searchType,
    #             "--verbose"
    #         ]

    #         logger.info(f"Executing command: {' '.join(cmd)}")

    #         # 启动进程，实时读取输出
    #         process = await asyncio.create_subprocess_exec(
    #             *cmd,
    #             stdout=asyncio.subprocess.PIPE,
    #             stderr=asyncio.subprocess.STDOUT,
    #             cwd=str(project_root),
    #             env={**os.environ, 'PYTHONIOENCODING': 'utf-8'}
    #         )

    #         # 发送开始信号
    #         yield f"data: {json.dumps({'type': 'start', 'message': f'开始搜索: {request.query}', 'timestamp': time.time()})}\n\n"

    #         # 收集完整输出用于最终返回
    #         full_output = []
            
    #         # 逐行读取输出并实时发送
    #         while True:
    #             line = await process.stdout.readline()
    #             if not line:
    #                 break
                
    #             line_text = line.decode('utf-8').strip()
    #             if line_text:
    #                 full_output.append(line_text)
    #                 # 实时发送每一行输出
    #                 yield f"data: {json.dumps({'type': 'process', 'message': line_text, 'timestamp': time.time()})}\n\n"

    #         # 等待进程结束
    #         return_code = await process.wait()
            
    #         if return_code == 0:
    #             # 发送完整结果
    #             final_result = '\n'.join(full_output)
    #             yield f"data: {json.dumps({'type': 'result', 'result': final_result, 'timestamp': time.time()})}\n\n"
    #             yield f"data: {json.dumps({'type': 'complete', 'message': '搜索完成', 'timestamp': time.time()})}\n\n"
    #         else:
    #             yield f"data: {json.dumps({'type': 'error', 'message': '搜索失败', 'timestamp': time.time()})}\n\n"

    #     except Exception as e:
    #         yield f"data: {json.dumps({'type': 'error', 'message': f'搜索过程中出错: {str(e)}', 'timestamp': time.time()})}\n\n"

    return StreamingResponse(
        generate_search_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*"
        }
    )
