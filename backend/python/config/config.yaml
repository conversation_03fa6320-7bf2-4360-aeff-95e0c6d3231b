log:
  dir: ./Logs
  name: app.log
  level: info
  max_size_m: 5

api:
  host: "0.0.0.0"
  port: 8080
  reload: false  # 启用热重载

  # 跨域配置
  cors:
    origins:
      - "http://localhost:3000"
      - "http://localhost:8080"
      - "http://127.0.0.1:3000"
      - "http://127.0.0.1:8080"
    allow_credentials: true
    allow_methods: [ "*" ]
    allow_headers: [ "*" ]

data:
  repos_path: ./data/repos

llm:
  base_url: "http://gpt-proxy.jd.com/v1"
  api_key: "15d429de-5cde-4f77-abb2-5e46c2f2e7aa"
  model: "anthropic.claude-3-5-sonnet-20241022-v2:0"
  temperature: 0.7
  max_tokens: 8192
  stream: False

deepsearch:
  max_iterations: 3  # 最大循环次数
  max_sub_queries: 3  # 最大子查询数量
  max_new_queries: 2  # 每次生成的新查询最大数量
  search_types: ["grep", "embedding"]  # 支持的搜索类型，未来可扩展为 ["grep", "embedding"]
  parallel_filter:
      enabled: True  # 是否启用并行过滤
      min_snippets_for_parallel: 5  # 启用并行过滤的最小代码片段数量
      max_workers: 10  # 最大并行线程数
      progress_interval: 5  # 进度显示间隔


file_filter:
  exclude: ['.git', '.svn', '.hg', 'node_modules', '__pycache__', '.pytest_cache', 'target', 'build', 'dist', '.next', '.vscode', '.idea', '.DS_Store', '*.pyc', '*.pyo', '*.pyd', '.venv']
  include: [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs", ".php", ".rb", ".md"]
  max_file_size: 1048576  # 1MB
