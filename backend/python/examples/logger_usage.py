#!/usr/bin/env python3
"""
日志系统使用示例

这个文件展示了如何在应用中使用日志系统。
"""

import sys
from pathlib import Path

# 添加backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.logger import logger, get_logger, info, warning, error, critical, debug


def main():
    """主函数，展示日志使用方法"""
    
    print("=== 日志系统使用示例 ===\n")
    
    # 1. 使用默认logger
    print("1. 使用默认logger:")
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    print()
    
    # 2. 使用便捷函数
    print("2. 使用便捷函数:")
    info("使用info函数记录信息")
    warning("使用warning函数记录警告")
    error("使用error函数记录错误")
    critical("使用critical函数记录严重错误")
    debug("使用debug函数记录调试信息")
    print()
    
    # 3. 获取命名logger
    print("3. 使用命名logger:")
    api_logger = get_logger("api")
    db_logger = get_logger("database")
    auth_logger = get_logger("auth")
    
    api_logger.info("API请求处理开始")
    db_logger.info("数据库连接建立")
    auth_logger.warning("用户认证失败")
    api_logger.error("API请求处理失败")
    print()
    
    # 4. 不同级别的日志
    print("4. 不同级别的日志示例:")
    logger.debug("调试信息：变量值为 x=10, y=20")
    logger.info("应用启动成功")
    logger.warning("配置文件使用默认值")
    logger.error("数据库连接失败")
    logger.critical("系统内存不足，即将关闭")
    print()
    
    # 5. 带格式化的日志
    print("5. 带格式化的日志:")
    user_id = 12345
    action = "登录"
    ip_address = "*************"
    
    logger.info(f"用户 {user_id} 从 {ip_address} 执行 {action} 操作")
    logger.warning("用户 %s 尝试访问受限资源", user_id)
    logger.error("操作失败: 用户=%d, 动作=%s, IP=%s", user_id, action, ip_address)
    print()
    
    # 6. 异常日志
    print("6. 异常日志记录:")
    try:
        # 模拟一个异常
        result = 10 / 0
    except ZeroDivisionError as e:
        logger.exception("除零错误发生")
        logger.error("计算错误: %s", str(e))
    print()
    
    # 7. 结构化日志
    print("7. 结构化日志:")
    logger.info("用户操作", extra={
        'user_id': user_id,
        'action': action,
        'ip': ip_address,
        'timestamp': '2025-08-13T12:00:00Z'
    })
    print()


def demonstrate_logger_in_class():
    """演示在类中使用logger"""
    
    class UserService:
        def __init__(self):
            self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        def create_user(self, username, email):
            self.logger.info(f"开始创建用户: {username}")
            
            try:
                # 模拟用户创建逻辑
                if not username:
                    raise ValueError("用户名不能为空")
                
                if "@" not in email:
                    raise ValueError("邮箱格式不正确")
                
                self.logger.info(f"用户创建成功: {username} ({email})")
                return {"id": 123, "username": username, "email": email}
                
            except ValueError as e:
                self.logger.error(f"用户创建失败: {e}")
                raise
            except Exception as e:
                self.logger.exception("用户创建过程中发生未知错误")
                raise
        
        def delete_user(self, user_id):
            self.logger.warning(f"删除用户请求: {user_id}")
            
            # 模拟删除逻辑
            if user_id <= 0:
                self.logger.error(f"无效的用户ID: {user_id}")
                return False
            
            self.logger.info(f"用户删除成功: {user_id}")
            return True
    
    print("\n=== 类中使用logger示例 ===")
    
    service = UserService()
    
    # 成功案例
    try:
        user = service.create_user("张三", "<EMAIL>")
        print(f"创建用户成功: {user}")
    except Exception as e:
        print(f"创建用户失败: {e}")
    
    # 失败案例
    try:
        service.create_user("", "invalid-email")
    except Exception as e:
        print(f"创建用户失败（预期）: {e}")
    
    # 删除用户
    service.delete_user(123)
    service.delete_user(-1)


def demonstrate_performance_logging():
    """演示性能日志记录"""
    
    import time
    
    print("\n=== 性能日志示例 ===")
    
    perf_logger = get_logger("performance")
    
    def timed_operation(operation_name, duration):
        """模拟一个耗时操作"""
        start_time = time.time()
        perf_logger.info(f"开始执行: {operation_name}")
        
        # 模拟耗时操作
        time.sleep(duration)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        if elapsed > 1.0:
            perf_logger.warning(f"操作耗时过长: {operation_name} 耗时 {elapsed:.2f}s")
        else:
            perf_logger.info(f"操作完成: {operation_name} 耗时 {elapsed:.2f}s")
    
    # 执行一些操作
    timed_operation("数据库查询", 0.1)
    timed_operation("文件上传", 0.5)
    timed_operation("大数据处理", 1.2)


def show_log_file_info():
    """显示日志文件信息"""
    
    from utils.logger import log_config, log_file_path
    
    print("\n=== 日志文件信息 ===")
    print(f"日志目录: {log_config.dir}")
    print(f"日志文件: {log_config.name}")
    print(f"日志级别: {log_config.level}")
    print(f"最大文件大小: {log_config.max_size_m}MB")
    print(f"完整路径: {log_file_path}")
    
    # 检查日志文件
    if log_file_path.exists():
        file_size = log_file_path.stat().st_size
        print(f"当前文件大小: {file_size} 字节 ({file_size/1024:.2f} KB)")
        
        # 显示最后几行日志
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print("\n最近的日志条目:")
                    for line in lines[-3:]:  # 显示最后3行
                        print(f"  {line.strip()}")
        except Exception as e:
            print(f"读取日志文件失败: {e}")
    else:
        print("日志文件不存在")


if __name__ == "__main__":
    main()
    demonstrate_logger_in_class()
    demonstrate_performance_logging()
    show_log_file_info()
    
    print("\n=== 日志系统示例完成 ===")
    print("请查看日志文件以确认所有日志都已正确记录。")
