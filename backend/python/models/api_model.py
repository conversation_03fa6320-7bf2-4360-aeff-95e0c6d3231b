from typing import List, Optional
from pydantic import BaseModel


# 数据模型
class WorkspaceInfo(BaseModel):
    id: str
    name: str
    path: str
    description: str = ""

class FileNode(BaseModel):
    id: str
    name: str
    type: str  # 'file' or 'directory'
    path: str
    size: Optional[int] = None
    lastModified: Optional[str] = None
    children: Optional[List['FileNode']] = None
    isExpanded: bool = False

class SearchRequest(BaseModel):
    query: str
    workspaceId: str
    searchType: str = "grep"  # 'grep' or 'embedding'
    maxResults: int = 50


class SearchResponse(BaseModel):
    searchId: str
    query: str
    result: str
    searchTime: float